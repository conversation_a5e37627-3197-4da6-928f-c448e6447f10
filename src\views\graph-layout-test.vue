<template>
  <div class="layout-test-container">
    <h2>G6 布局切换测试</h2>
    <p>测试三种布局模式：径向布局、同心圆布局、层次布局</p>

    <div class="graph-container">
      <GraphViewG6 :nodes="nodes" :links="links" :showArrows="true" :arrowSize="20" :r="20" />
    </div>
  </div>
</template>

<script setup>
import GraphViewG6 from "@/components/graph/g6v5.vue";

// 创建一个更清晰的测试数据结构
const testData = {
  nodes: [
    // 中心节点
    { id: "center", nodeName: "总部", label: "指挥部", entityId: "HQ001" },

    // 第一层节点
    { id: "div1", nodeName: "第1师", label: "师级", entityId: "DIV001" },
    { id: "div2", nodeName: "第2师", label: "师级", entityId: "DIV002" },
    { id: "div3", nodeName: "第3师", label: "师级", entityId: "DIV003" },

    // 第二层节点
    { id: "reg1", nodeName: "第1团", label: "团级", entityId: "REG001" },
    { id: "reg2", nodeName: "第2团", label: "团级", entityId: "REG002" },
    { id: "reg3", nodeName: "第3团", label: "团级", entityId: "REG003" },
    { id: "reg4", nodeName: "第4团", label: "团级", entityId: "REG004" },

    // 第三层节点
    { id: "bat1", nodeName: "第1营", label: "营级", entityId: "BAT001" },
    { id: "bat2", nodeName: "第2营", label: "营级", entityId: "BAT002" },
    { id: "bat3", nodeName: "第3营", label: "营级", entityId: "BAT003" },
    { id: "bat4", nodeName: "第4营", label: "营级", entityId: "BAT004" },
  ],
  links: [
    // 总部到师级
    { id: "l1", name: "指挥", source: "center", target: "div1" },
    { id: "l2", name: "指挥", source: "center", target: "div2" },
    { id: "l3", name: "指挥", source: "center", target: "div3" },

    // 师级到团级
    { id: "l4", name: "管辖", source: "div1", target: "reg1" },
    { id: "l5", name: "管辖", source: "div1", target: "reg2" },
    { id: "l6", name: "管辖", source: "div2", target: "reg3" },
    { id: "l7", name: "管辖", source: "div3", target: "reg4" },

    // 团级到营级
    { id: "l8", name: "统领", source: "reg1", target: "bat1" },
    { id: "l9", name: "统领", source: "reg2", target: "bat2" },
    { id: "l10", name: "统领", source: "reg3", target: "bat3" },
    { id: "l11", name: "统领", source: "reg4", target: "bat4" },
  ],
};

const nodes = testData.nodes.map((n) => ({
  id: String(n.id),
  nodeName: n.nodeName,
  label: n.label,
  entityId: n.entityId,
  attrs: [
    { code: "name", name: "名称", value: n.nodeName, sortId: 0, isSystem: false },
    { code: "type", name: "类型", value: n.label, sortId: 1, isSystem: false },
    { code: "id", name: "编号", value: n.entityId, sortId: 2, isSystem: false },
  ],
}));

const links = testData.links.map((l) => ({
  id: String(l.id),
  name: l.name,
  source: String(l.source),
  target: String(l.target),
}));
</script>

<style scoped>
.layout-test-container {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-test-container h2 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 24px;
}

.layout-test-container p {
  margin: 0 0 20px 0;
  color: #7f8c8d;
  font-size: 14px;
}

.graph-container {
  flex: 1;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  overflow: hidden;
  background: #f8f9fa;
}
</style>

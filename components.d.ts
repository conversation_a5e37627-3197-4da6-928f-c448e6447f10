// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    2: typeof import('./src/components/SpecChart/index copy 2.vue')['default']
    copy: typeof import('./src/components/graph/index copy.vue')['default']
    Countup: typeof import('./src/components/countup.vue')['default']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    G6v5: typeof import('./src/components/graph/g6v5.vue')['default']
    Graph: typeof import('./src/components/graph/index.vue')['default']
    Header: typeof import('./src/components/header.vue')['default']
    'Index copy': typeof import('./src/components/graph/index copy.vue')['default']
    IndexNew: typeof import('./src/components/graph/index-new.vue')['default']
    IndexV5: typeof import('./src/components/graph/index-v5.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Sidebar: typeof import('./src/components/sidebar.vue')['default']
    SimpleTest: typeof import('./src/components/graph/simple-test.vue')['default']
    SpecChart: typeof import('./src/components/SpecChart/index.vue')['default']
    TableCustom: typeof import('./src/components/table-custom.vue')['default']
    TableDetail: typeof import('./src/components/table-detail.vue')['default']
    TableEdit: typeof import('./src/components/table-edit.vue')['default']
    TableSearch: typeof import('./src/components/table-search.vue')['default']
    Tabs: typeof import('./src/components/tabs.vue')['default']
  }
}

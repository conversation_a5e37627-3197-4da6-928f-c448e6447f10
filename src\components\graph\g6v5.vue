<template>
  <div class="g6-panel">
    <!-- 控件：布局选择 -->
    <div class="toolbar">
      <label>布局模式：</label>
      <select v-model="selectedLayout" @change="applyLayout">
        <option value="radial">径向布局 (Radial)</option>
        <option value="concentric">同心圆布局 (Concentric)</option>
        <option value="dagre">层次布局 (Dagre)</option>
      </select>
    </div>

    <div ref="wrapRef" class="g6-wrap"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, computed } from "vue";
import { Graph } from "@antv/g6";

const props = defineProps({
  nodes: { type: Array, required: true }, // [{id, nodeName, label, entityId, attrs}]
  links: { type: Array, required: true }, // [{id, name, source, target}]
  r: { type: Number, default: 16 },
  showArrows: { type: Boolean, default: true },
  arrowSize: { type: Number, default: 20 },
  force: {
    type: Object,
    default: () => ({
      linkDistance: 120,
      nodeStrength: -350,
      edgeStrength: 0.2,
    }),
  },
});

const wrapRef = ref(null);
let graph = null;

const selectedLayout = ref("radial"); // 默认径向

/* =============== 小工具 =============== */

// 更鲜艳的颜色映射（按 label）
const colorByLabel = (label) => {
  const palette = ["#1890ff", "#52c41a", "#faad14", "#f5222d", "#722ed1", "#13c2c2", "#eb2f96", "#fa8c16"];
  const idx = Math.abs((label || "").split("").reduce((s, c) => s + c.charCodeAt(0), 0)) % palette.length;
  return palette[idx];
};

// 计算度数最高的节点（径向布局的默认中心）
const centerId = computed(() => {
  const deg = new Map();
  props.nodes.forEach((n) => deg.set(String(n.id), 0));
  props.links.forEach((e) => {
    const s = String(e.source),
      t = String(e.target);
    deg.set(s, (deg.get(s) || 0) + 1);
    deg.set(t, (deg.get(t) || 0) + 1);
  });
  let best = props.nodes?.[0]?.id ? String(props.nodes[0].id) : null;
  let bestVal = -1;
  for (const [k, v] of deg.entries())
    if (v > bestVal) {
      best = k;
      bestVal = v;
    }
  return best;
});

// 以 center 为根做 BFS 层级（供 concentric 使用）
function bfsLevels(rootId) {
  // 构建邻接表
  const adj = new Map();
  props.nodes.forEach((n) => adj.set(String(n.id), new Set()));
  props.links.forEach((e) => {
    const s = String(e.source);
    const t = String(e.target);
    adj.get(s)?.add(t);
    adj.get(t)?.add(s);
  });
  
  // 初始化距离映射
  const dist = new Map();
  props.nodes.forEach((n) => dist.set(String(n.id), Infinity));
  
  // 如果没有根节点或根节点不存在，返回默认距离
  if (!rootId || !adj.has(rootId)) {
    // 将所有节点设为同一层级
    props.nodes.forEach((n) => dist.set(String(n.id), 1));
    return dist;
  }
  
  // BFS 计算层级
  const queue = [rootId];
  dist.set(rootId, 0);
  
  while (queue.length > 0) {
    const current = queue.shift();
    const currentDist = dist.get(current);
    
    for (const neighbor of adj.get(current) || []) {
      if (dist.get(neighbor) === Infinity) {
        dist.set(neighbor, currentDist + 1);
        queue.push(neighbor);
      }
    }
  }
  
  return dist;
}

/* =============== 数据适配 =============== */

const toG6Data = () => {
  const nodes = props.nodes.map((n) => ({
    id: String(n.id),
    data: {
      label: n.nodeName || n.id,
      nodeName: n.nodeName,
      entityId: n.entityId,
      rawLabel: n.label,
      attrs: n.attrs || [],
    },
  }));

  const edges = props.links.map((l) => ({
    id: String(l.id),
    source: String(l.source),
    target: String(l.target),
    data: {
      label: l.name || "",
    },
  }));

  return { nodes, edges };
};

/* =============== 布局配置 =============== */

function getLayoutConfig(kind) {
  if (kind === "radial") {
    return {
      type: "radial",
      unitRadius: Math.max(80, props.r * 6),
      focusNode: centerId.value,
      linkDistance: props.force.linkDistance || 120,
      nodeSize: props.r * 2.5,
      preventOverlap: true,
      strictRadial: true,
      maxIteration: 1000,
    };
  }

  if (kind === "concentric") {
    // 同心圆布局，按 BFS 层级向外扩散
    const dist = bfsLevels(centerId.value);
    
    return {
      type: "concentric",
      preventOverlap: true,
      nodeSize: props.r * 2.5,
      nodeSpacing: Math.max(40, props.r * 2),
      equidistant: false,
      startAngle: 0,
      clockwise: true,
      maxLevelDiff: 4,
      sortBy: (nodeA, nodeB) => {
        // 根据BFS距离排序，距离越小越靠近中心
        const distA = dist.get(nodeA.id) || Infinity;
        const distB = dist.get(nodeB.id) || Infinity;
        return distA - distB;
      },
    };
  }

  // dagre 层次布局
  return {
    type: "dagre",
    rankdir: "TB",
    align: "UL",
    nodesep: Math.max(40, props.r * 2),
    ranksep: Math.max(60, props.r * 4),
    ranker: "network-simplex",
    controlPoints: false,
    nodeSize: [props.r * 2.5, props.r * 2.5],
  };
}

async function applyLayout() {
  if (!graph) return;
  
  console.log(`切换到布局: ${selectedLayout.value}`);
  
  // 清除之前的高亮状态
  clearHighlight();
  
  // 获取新的布局配置
  const layoutConfig = getLayoutConfig(selectedLayout.value);
  console.log("布局配置:", layoutConfig);
  
  // G6 v5 API: 设置布局并执行
  graph.setLayout(layoutConfig);
  await graph.layout();
  
  // 自适应视图
  graph.fitView();
  
  console.log("布局完成");
}

/* =============== 构建图 =============== */

function build() {
  const { clientWidth, clientHeight } = wrapRef.value;

  graph = new Graph({
    container: wrapRef.value,
    width: Math.max(300, clientWidth),
    height: Math.max(240, clientHeight),
    autoFit: "view",
    data: toG6Data(),
    node: {
      style: {
        size: props.r * 2.5,
        fill: (d) => colorByLabel(d.data.rawLabel),
        stroke: "#fff",
        lineWidth: 3,
        labelText: (d) => d.data.label,
        labelFill: "#333",
        labelFontSize: 14,
        labelFontWeight: "bold",
        labelPlacement: "bottom",
        labelOffsetY: 10,
      },
      state: {
        highlight: { 
          stroke: "#ff4d4f", 
          lineWidth: 4, 
        },
        dark: { 
          opacity: 0.3 
        },
      },
    },
    edge: {
      style: {
        stroke: "#1890ff",
        lineWidth: 3,
        opacity: 0.9,
        labelText: (d) => d.data.label,
        labelFill: "#1890ff",
        labelFontSize: 13,
        labelFontWeight: "bold",
        endArrow: props.showArrows ? {
          fill: "#1890ff",
          size: props.arrowSize,
        } : false,
      },
      state: {
        highlight: {
          stroke: "#ff4d4f",
          lineWidth: 4,
          opacity: 1,
        },
        dark: { 
          opacity: 0.2 
        },
      },
    },
    layout: getLayoutConfig(selectedLayout.value),
    behaviors: ["drag-canvas", "zoom-canvas", "drag-element"],
  });

  graph.render();

  // 交互：高亮邻居
  graph.on("node:click", (ev) => {
    const id = ev.itemId;
    highlightNeighbors(id);
  });
  graph.on("canvas:click", () => clearHighlight());

  // 容器自适应
  const ro = new ResizeObserver(() => {
    if (!graph || graph.destroyed) return;
    const { clientWidth: w, clientHeight: h } = wrapRef.value;
    graph.setSize([Math.max(300, w), Math.max(240, h)]);
  });
  ro.observe(wrapRef.value);
  graph.__ro__ = ro;
}

/* =============== 高亮 =============== */

function clearHighlight() {
  if (!graph) return;
  graph.getAllNodesData().forEach((n) => {
    graph.setElementState(n.id, ["highlight", "dark"], false);
  });
  graph.getAllEdgesData().forEach((e) => {
    graph.setElementState(e.id, ["highlight", "dark"], false);
  });
}

function highlightNeighbors(centerId) {
  if (!graph) return;
  clearHighlight();

  const neighborIds = graph.getNeighborNodesData(centerId).map(n => n.id);
  const relatedEdgeIds = graph.getAllEdgesData()
    .filter(e => e.source === centerId || e.target === centerId)
    .map(e => e.id);

  // 设置暗化状态
  graph.getAllNodesData().forEach((n) => {
    if (n.id !== centerId && !neighborIds.includes(n.id)) {
      graph.setElementState(n.id, "dark", true);
    }
  });
  graph.getAllEdgesData().forEach((e) => {
    if (!relatedEdgeIds.includes(e.id)) {
      graph.setElementState(e.id, "dark", true);
    }
  });

  // 高亮中心节点和相关边
  graph.setElementState(centerId, "highlight", true);
  relatedEdgeIds.forEach((id) => {
    graph.setElementState(id, "highlight", true);
  });
}

/* =============== 生命周期 =============== */

onMounted(build);

onBeforeUnmount(() => {
  if (graph?.__ro__) graph.__ro__.disconnect?.();
  graph?.destroy?.();
});

// 数据变化：刷新
watch(
  () => [props.nodes, props.links],
  () => {
    if (!graph) return;
    graph.setData(toG6Data());
    applyLayout();
  },
  { deep: true }
);
</script>

<style scoped>
.g6-panel {
  position: relative;
  width: 100%;
  height: 100%;
}

.toolbar {
  position: absolute;
  z-index: 10;
  top: 15px;
  left: 15px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 8px 12px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(8px);
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  transition: all 0.2s ease;
}

.toolbar:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

.toolbar label {
  color: #6b7280;
  font-weight: 600;
  white-space: nowrap;
}

.toolbar select {
  appearance: none;
  border: 1px solid #d1d5db;
  background: #fff;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  outline: none;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 160px;
  padding-right: 32px;
}

.toolbar select:hover {
  border-color: #9ca3af;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.toolbar select:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.g6-wrap {
  width: 100%;
  height: 100%;
  min-height: 480px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>

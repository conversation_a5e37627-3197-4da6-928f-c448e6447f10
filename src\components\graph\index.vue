<template>
  <div class="g6-panel">
    <!-- 控件：布局选择 -->
    <div class="toolbar">
      <label>布局模式：</label>
      <select v-model="selectedLayout" @change="applyLayout">
        <option value="radial">径向布局 (Radial)</option>
        <option value="eco">同心圆布局 (Concentric)</option>
        <option value="vertical">层次布局 (Dagre)</option>
      </select>
    </div>

    <div ref="wrapRef" class="g6-wrap"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, computed } from "vue";
import * as G6 from "@antv/g6";

const props = defineProps({
  nodes: { type: Array, required: true }, // [{id, nodeName, label, entityId, attrs}]
  links: { type: Array, required: true }, // [{id, name, source, target}]
  r: { type: Number, default: 16 },
  showArrows: { type: <PERSON>olean, default: true },
  arrowSize: { type: Number, default: 20 },
  force: {
    type: Object,
    default: () => ({
      linkDistance: 120,
      nodeStrength: -350,
      edgeStrength: 0.2,
    }),
  },
});

const wrapRef = ref(null);
let graph = null;
let tooltip = null;
let minimap = null;

const selectedLayout = ref("radial"); // 默认径向

/* =============== 小工具 =============== */

// 更鲜艳的颜色映射（按 label）
const colorByLabel = (label) => {
  const palette = ["#1890ff", "#52c41a", "#faad14", "#f5222d", "#722ed1", "#13c2c2", "#eb2f96", "#fa8c16"];
  const idx = Math.abs((label || "").split("").reduce((s, c) => s + c.charCodeAt(0), 0)) % palette.length;
  return palette[idx];
};

// 计算度数最高的节点（生态树/径向的默认中心）
const centerId = computed(() => {
  const deg = new Map();
  props.nodes.forEach((n) => deg.set(String(n.id), 0));
  props.links.forEach((e) => {
    const s = String(e.source),
      t = String(e.target);
    deg.set(s, (deg.get(s) || 0) + 1);
    deg.set(t, (deg.get(t) || 0) + 1);
  });
  let best = props.nodes?.[0]?.id ? String(props.nodes[0].id) : null;
  let bestVal = -1;
  for (const [k, v] of deg.entries())
    if (v > bestVal) {
      best = k;
      bestVal = v;
    }
  return best;
});

// 以 center 为根做 BFS 层级（供 concentric 使用）
function bfsLevels(rootId) {
  // 构建邻接表
  const adj = new Map();
  props.nodes.forEach((n) => adj.set(String(n.id), new Set()));
  props.links.forEach((e) => {
    const s = String(e.source);
    const t = String(e.target);
    adj.get(s)?.add(t);
    adj.get(t)?.add(s);
  });

  // 初始化距离映射
  const dist = new Map();
  props.nodes.forEach((n) => dist.set(String(n.id), Infinity));

  // 如果没有根节点或根节点不存在，返回默认距离
  if (!rootId || !adj.has(rootId)) {
    // 将所有节点设为同一层级
    props.nodes.forEach((n) => dist.set(String(n.id), 1));
    return dist;
  }

  // BFS 计算层级
  const queue = [rootId];
  dist.set(rootId, 0);

  while (queue.length > 0) {
    const current = queue.shift();
    const currentDist = dist.get(current);

    for (const neighbor of adj.get(current) || []) {
      if (dist.get(neighbor) === Infinity) {
        dist.set(neighbor, currentDist + 1);
        queue.push(neighbor);
      }
    }
  }

  return dist;
}

/* =============== 数据适配 =============== */

const toG6Data = () => {
  const nodes = props.nodes.map((n) => ({
    id: String(n.id),
    label: n.nodeName || n.id,
    nodeName: n.nodeName,
    entityId: n.entityId,
    rawLabel: n.label,
    attrs: n.attrs || [],
    size: props.r * 2.5,
    style: {
      fill: colorByLabel(n.label),
      stroke: "#fff",
      lineWidth: 3,
      shadowColor: "rgba(0,0,0,0.1)",
      shadowBlur: 6,
    },
    labelCfg: {
      position: "bottom",
      offset: 10,
      style: { fill: "#333", fontSize: 14, fontWeight: "bold" },
    },
  }));

  // 用 polyline + curveOffset: 0，确保“绝对直线”，箭头方向稳定
  const edges = props.links.map((l) => ({
    id: String(l.id),
    source: String(l.source),
    target: String(l.target),
    label: l.name || "",
    type: "polyline",
    curveOffset: 0,
    style: {
      stroke: "#1890ff",
      lineWidth: 3,
      lineAppendWidth: 8,
      opacity: 0.9,
      endArrow: props.showArrows
        ? {
            path: G6.Arrow.triangle(props.arrowSize * 0.8, props.arrowSize, 0),
            d: props.arrowSize,
            fill: "#1890ff",
            stroke: "#1890ff",
            lineWidth: 1,
          }
        : false,
    },
    labelCfg: {
      autoRotate: true,
      refY: -8,
      style: {
        fill: "#1890ff",
        fontSize: 13,
        fontWeight: "bold",
        opacity: 0.9,
        background: { fill: "#fff", stroke: "#1890ff", padding: [2, 4, 2, 4], radius: 3 },
      },
    },
  }));

  return { nodes, edges };
};

/* =============== 布局配置 =============== */

function getLayoutConfig(kind) {
  if (kind === "radial") {
    return {
      type: "radial",
      unitRadius: Math.max(80, props.r * 6), // 增加单位半径
      focusNode: centerId.value,
      maxIteration: 1000,
      linkDistance: props.force.linkDistance || 120,
      nodeSize: props.r * 2.5, // 节点大小用于碰撞检测
      preventOverlap: true,
      strictRadial: true, // 严格径向布局
      sortBy: undefined, // 不排序，保持原始结构
      sortStrength: 10,
    };
  }

  if (kind === "eco") {
    // 生态树：同心圆布局，按 BFS 层级向外扩散
    const dist = bfsLevels(centerId.value);

    // G6 v4 concentric layout 配置
    return {
      type: "concentric",
      preventOverlap: true,
      nodeSize: props.r * 2.5,
      minNodeSpacing: Math.max(40, props.r * 2),
      equidistant: false,
      startAngle: 0,
      clockwise: true,
      maxLevelDiff: 4,
      sortBy: "concentric", // 使用内置排序字段
      concentric: (node) => {
        // 根据BFS距离计算同心圆层级，距离越小分数越高（越靠近中心）
        const distance = dist.get(node.id);
        if (distance === undefined || distance === Infinity) return -1000;
        return -distance; // 负值，距离越小分数越高
      },
    };
  }

  // 垂直布局：dagre（自上而下的层次布局）
  return {
    type: "dagre",
    rankdir: "TB", // TB: 上->下，LR: 左->右，BT: 下->上，RL: 右->左
    align: "UL", // UL: 左上对齐，UR: 右上，DL: 左下，DR: 右下
    nodesep: Math.max(40, props.r * 2), // 同层节点间距
    ranksep: Math.max(60, props.r * 4), // 层间距离
    ranker: "network-simplex", // 排序算法：network-simplex | tight-tree | longest-path
    controlPoints: false, // 不保留边的控制点，配合 polyline 显示直线
    nodeSize: [props.r * 2.5, props.r * 2.5], // [width, height] 用于布局计算
  };
}

function applyLayout() {
  if (!graph) return;

  console.log(`切换到布局: ${selectedLayout.value}`);

  // 清除之前的高亮状态
  clearHighlight();

  // 获取新的布局配置
  const layoutConfig = getLayoutConfig(selectedLayout.value);
  console.log("布局配置:", layoutConfig);

  // 应用新布局 - G6 v4 API
  graph.updateLayout(layoutConfig);

  // 布局完成后的回调 - G6 v4 API
  graph.once("afterlayout", () => {
    console.log("布局完成");
    // 自适应视图，添加一些边距
    graph.fitView(40);

    // 如果是径向布局，确保中心节点可见
    if (selectedLayout.value === "radial" && centerId.value) {
      const centerNode = graph.findById(centerId.value);
      if (centerNode) {
        console.log("径向布局中心节点:", centerId.value);
        // 可以选择性地高亮中心节点
        // graph.setItemState(centerNode, "highlight", true);
      }
    }
  });
}

/* =============== 构建图 =============== */

function build() {
  const { clientWidth, clientHeight } = wrapRef.value;

  tooltip = new G6.Tooltip({
    offsetX: 12,
    offsetY: 12,
    fixToNode: [1, 0],
    itemTypes: ["node"],
    getContent: (e) => {
      const model = e?.item?.getModel?.() || {};
      const el = document.createElement("div");
      el.className = "g6-tt";
      const type = model.rawLabel ? `<div class="sub">类型：${model.rawLabel}</div>` : "";
      const eid = model.entityId ? `<div class="sub">ID：${model.entityId}</div>` : "";
      el.innerHTML = `<div class="name">${model.nodeName || model.id}</div>${eid}${type}`;
      return el;
    },
  });

  minimap = new G6.Minimap({ size: [160, 110], type: "keyShape" });

  graph = new G6.Graph({
    container: wrapRef.value,
    width: Math.max(300, clientWidth),
    height: Math.max(240, clientHeight),
    plugins: [tooltip, minimap],
    modes: { default: ["drag-canvas", "zoom-canvas", "drag-node"] },
    defaultNode: {
      type: "circle",
      style: { cursor: "pointer", shadowColor: "rgba(0,0,0,0.1)", shadowBlur: 6 },
      labelCfg: { style: { cursor: "default", fontWeight: "bold" } },
    },
    defaultEdge: {
      type: "polyline",
      style: {
        stroke: "#1890ff",
        lineWidth: 3,
        lineAppendWidth: 8,
        opacity: 0.9,
        endArrow: props.showArrows
          ? {
              path: G6.Arrow.triangle(props.arrowSize * 0.8, props.arrowSize, 0),
              d: props.arrowSize,
              fill: "#1890ff",
              stroke: "#1890ff",
              lineWidth: 1,
            }
          : false,
      },
      curveOffset: 0,
    },
    nodeStateStyles: {
      highlight: { stroke: "#ff4d4f", lineWidth: 4, shadowColor: "rgba(255,77,79,0.3)", shadowBlur: 10 },
      dark: { opacity: 0.3 },
    },
    edgeStateStyles: {
      highlight: {
        stroke: "#ff4d4f",
        lineWidth: 4,
        opacity: 1,
        shadowColor: "rgba(255,77,79,0.2)",
        shadowBlur: 8,
        endArrow: { path: G6.Arrow.triangle(18, 22, 0), d: 22, fill: "#ff4d4f", stroke: "#ff4d4f", lineWidth: 1 },
      },
      dark: { opacity: 0.2 },
    },
    layout: getLayoutConfig(selectedLayout.value),
    animate: true,
    fitCenter: true,
  });

  // 交互：高亮邻居
  graph.on("node:click", (ev) => {
    const id = ev.item.getID();
    highlightNeighbors(id);
  });
  graph.on("canvas:click", () => clearHighlight());

  graph.data(toG6Data());
  graph.render();
  graph.fitView(30);

  // 容器自适应
  const ro = new ResizeObserver(() => {
    if (!graph || graph.get("destroyed")) return;
    const { clientWidth: w, clientHeight: h } = wrapRef.value;
    graph.changeSize(Math.max(300, w), Math.max(240, h));
  });
  ro.observe(wrapRef.value);
  graph.__ro__ = ro;
}

/* =============== 高亮 =============== */

function clearHighlight() {
  if (!graph) return;
  graph.setAutoPaint(false);
  graph.getNodes().forEach((n) => graph.clearItemStates(n));
  graph.getEdges().forEach((e) => graph.clearItemStates(e));
  graph.paint();
  graph.setAutoPaint(true);
}

function highlightNeighbors(centerId) {
  if (!graph) return;
  clearHighlight();

  const center = graph.findById(centerId);
  if (!center) return;

  const neighborNodes = graph.getNeighbors(center, "both");
  const relatedEdges = graph.getEdges().filter((e) => {
    const m = e.getModel();
    return m.source === centerId || m.target === centerId;
  });

  graph.getNodes().forEach((n) => graph.setItemState(n, "dark", true));
  graph.getEdges().forEach((e) => graph.setItemState(e, "dark", true));

  graph.setItemState(center, "dark", false);
  graph.setItemState(center, "highlight", true);
  neighborNodes.forEach((n) => graph.setItemState(n, "dark", false));
  relatedEdges.forEach((e) => {
    graph.setItemState(e, "dark", false);
    graph.setItemState(e, "highlight", true);
  });
}

/* =============== 生命周期 =============== */

onMounted(build);

onBeforeUnmount(() => {
  if (graph?.__ro__) graph.__ro__.disconnect?.();
  graph?.destroy?.();
});

// 数据变化：刷新
watch(
  () => [props.nodes, props.links],
  () => {
    if (!graph) return;
    graph.changeData(toG6Data());
    applyLayout();
  },
  { deep: true }
);
</script>

<style scoped>
.g6-panel {
  position: relative;
  width: 100%;
  height: 100%;
}

.toolbar {
  position: absolute;
  z-index: 10;
  top: 15px;
  left: 15px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 8px 12px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(8px);
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  transition: all 0.2s ease;
}

.toolbar:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

.toolbar label {
  color: #6b7280;
  font-weight: 600;
  white-space: nowrap;
}

.toolbar select {
  appearance: none;
  border: 1px solid #d1d5db;
  background: #fff;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  outline: none;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 160px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 32px;
}

.toolbar select:hover {
  border-color: #9ca3af;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.toolbar select:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.g6-wrap {
  width: 100%;
  height: 100%;
  min-height: 480px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Tooltip 外观 */
.g6-tt {
  background: rgba(20, 24, 36, 0.92);
  color: #fff;
  padding: 8px 10px;
  border-radius: 8px;
  font-size: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.18);
  max-width: 240px;
}
.g6-tt .name {
  font-weight: 600;
  margin-bottom: 2px;
}
.g6-tt .sub {
  opacity: 0.9;
}
</style>

<template>
  <div class="g6-panel">
    <!-- 控件：布局选择 -->
    <div class="toolbar">
      <label>布局：</label>
      <select v-model="selectedLayout" @change="applyLayout">
        <option value="radial">径向布局</option>
        <option value="eco">生态树</option>
        <option value="vertical">垂直布局</option>
      </select>
    </div>

    <div ref="wrapRef" class="g6-wrap"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, computed } from "vue";
import * as G6 from "@antv/g6";

const props = defineProps({
  nodes: { type: Array, required: true }, // [{id, nodeName, label, entityId, attrs}]
  links: { type: Array, required: true }, // [{id, name, source, target}]
  r: { type: Number, default: 16 },
  showArrows: { type: Boolean, default: true },
  arrowSize: { type: Number, default: 20 },
  force: {
    type: Object,
    default: () => ({
      linkDistance: 120,
      nodeStrength: -350,
      edgeStrength: 0.2,
    }),
  },
});

const wrapRef = ref(null);
let graph = null;
let tooltip = null;
let minimap = null;

const selectedLayout = ref("radial"); // 默认径向

/* =============== 小工具 =============== */

// 更鲜艳的颜色映射（按 label）
const colorByLabel = (label) => {
  const palette = ["#1890ff", "#52c41a", "#faad14", "#f5222d", "#722ed1", "#13c2c2", "#eb2f96", "#fa8c16"];
  const idx = Math.abs((label || "").split("").reduce((s, c) => s + c.charCodeAt(0), 0)) % palette.length;
  return palette[idx];
};

// 计算度数最高的节点（生态树/径向的默认中心）
const centerId = computed(() => {
  const deg = new Map();
  props.nodes.forEach((n) => deg.set(String(n.id), 0));
  props.links.forEach((e) => {
    const s = String(e.source),
      t = String(e.target);
    deg.set(s, (deg.get(s) || 0) + 1);
    deg.set(t, (deg.get(t) || 0) + 1);
  });
  let best = props.nodes?.[0]?.id ? String(props.nodes[0].id) : null;
  let bestVal = -1;
  for (const [k, v] of deg.entries())
    if (v > bestVal) {
      best = k;
      bestVal = v;
    }
  return best;
});

// 以 center 为根做 BFS 层级（供 concentric 使用）
function bfsLevels(rootId) {
  const adj = new Map();
  props.nodes.forEach((n) => adj.set(String(n.id), new Set()));
  props.links.forEach((e) => {
    const s = String(e.source),
      t = String(e.target);
    adj.get(s)?.add(t);
    adj.get(t)?.add(s);
  });
  const dist = new Map(props.nodes.map((n) => [String(n.id), Infinity]));
  if (!rootId || !adj.has(rootId)) return dist;
  const q = [rootId];
  dist.set(rootId, 0);
  while (q.length) {
    const u = q.shift();
    for (const v of adj.get(u) || []) {
      if (dist.get(v) === Infinity) {
        dist.set(v, dist.get(u) + 1);
        q.push(v);
      }
    }
  }
  return dist;
}

/* =============== 数据适配 =============== */

const toG6Data = () => {
  const nodes = props.nodes.map((n) => ({
    id: String(n.id),
    label: n.nodeName || n.id,
    nodeName: n.nodeName,
    entityId: n.entityId,
    rawLabel: n.label,
    attrs: n.attrs || [],
    size: props.r * 2.5,
    style: {
      fill: colorByLabel(n.label),
      stroke: "#fff",
      lineWidth: 3,
      shadowColor: "rgba(0,0,0,0.1)",
      shadowBlur: 6,
    },
    labelCfg: {
      position: "bottom",
      offset: 10,
      style: { fill: "#333", fontSize: 14, fontWeight: "bold" },
    },
  }));

  // 用 polyline + curveOffset: 0，确保“绝对直线”，箭头方向稳定
  const edges = props.links.map((l) => ({
    id: String(l.id),
    source: String(l.source),
    target: String(l.target),
    label: l.name || "",
    type: "polyline",
    curveOffset: 0,
    style: {
      stroke: "#1890ff",
      lineWidth: 3,
      lineAppendWidth: 8,
      opacity: 0.9,
      endArrow: props.showArrows
        ? {
            path: G6.Arrow.triangle(props.arrowSize * 0.8, props.arrowSize, 0),
            d: props.arrowSize,
            fill: "#1890ff",
            stroke: "#1890ff",
            lineWidth: 1,
          }
        : false,
    },
    labelCfg: {
      autoRotate: true,
      refY: -8,
      style: {
        fill: "#1890ff",
        fontSize: 13,
        fontWeight: "bold",
        opacity: 0.9,
        background: { fill: "#fff", stroke: "#1890ff", padding: [2, 4, 2, 4], radius: 3 },
      },
    },
  }));

  return { nodes, edges };
};

/* =============== 布局配置 =============== */

function getLayoutConfig(kind) {
  // 共用参数
  const common = { preventOverlap: true, strictRadial: false };

  if (kind === "radial") {
    return {
      type: "radial",
      unitRadius: Math.max(60, props.r * 5),
      focusNode: centerId.value,
      maxIteration: 2000,
      linkDistance: props.force.linkDistance,
      nodeSize: props.r * 3,
      ...common,
    };
  }

  if (kind === "eco") {
    // 生态树：同心圆，按 BFS 层级向外扩散
    const dist = bfsLevels(centerId.value);
    // concentric 接收一个分数，分高在中心
    const score = (n) => {
      const d = dist.get(n.id);
      return Number.isFinite(d) ? -d : -9999; // root 最大（0→-0）
    };
    return {
      type: "concentric",
      minNodeSpacing: 50,
      equidistant: false,
      maxLevelDiff: 4,
      sortBy: "concentric",
      concentric: score,
      nodeSize: props.r * 3,
      sweep: 2 * Math.PI,
      ...common,
    };
  }

  // 垂直布局：dagre（自上而下）
  return {
    type: "dagre",
    rankdir: "TB", // TB: 上->下 ； LR: 左->右
    align: "UL", // 顶对齐
    nodesep: 30,
    ranksep: 80,
    controlPoints: false, // 关闭折点，配合 polyline 直线显示
  };
}

function applyLayout() {
  if (!graph) return;
  graph.updateLayout(getLayoutConfig(selectedLayout.value));
  // 让布局完成后自适应视图
  graph.once("afterlayout", () => graph.fitView(30));
}

/* =============== 构建图 =============== */

function build() {
  const { clientWidth, clientHeight } = wrapRef.value;

  tooltip = new G6.Tooltip({
    offsetX: 12,
    offsetY: 12,
    fixToNode: [1, 0],
    itemTypes: ["node"],
    getContent: (e) => {
      const model = e?.item?.getModel?.() || {};
      const el = document.createElement("div");
      el.className = "g6-tt";
      const type = model.rawLabel ? `<div class="sub">类型：${model.rawLabel}</div>` : "";
      const eid = model.entityId ? `<div class="sub">ID：${model.entityId}</div>` : "";
      el.innerHTML = `<div class="name">${model.nodeName || model.id}</div>${eid}${type}`;
      return el;
    },
  });

  minimap = new G6.Minimap({ size: [160, 110], type: "keyShape" });

  graph = new G6.Graph({
    container: wrapRef.value,
    width: Math.max(300, clientWidth),
    height: Math.max(240, clientHeight),
    plugins: [tooltip, minimap],
    modes: { default: ["drag-canvas", "zoom-canvas", "drag-node"] },
    defaultNode: {
      type: "circle",
      style: { cursor: "pointer", shadowColor: "rgba(0,0,0,0.1)", shadowBlur: 6 },
      labelCfg: { style: { cursor: "default", fontWeight: "bold" } },
    },
    defaultEdge: {
      type: "polyline",
      style: {
        stroke: "#1890ff",
        lineWidth: 3,
        lineAppendWidth: 8,
        opacity: 0.9,
        endArrow: props.showArrows
          ? {
              path: G6.Arrow.triangle(props.arrowSize * 0.8, props.arrowSize, 0),
              d: props.arrowSize,
              fill: "#1890ff",
              stroke: "#1890ff",
              lineWidth: 1,
            }
          : false,
      },
      curveOffset: 0,
    },
    nodeStateStyles: {
      highlight: { stroke: "#ff4d4f", lineWidth: 4, shadowColor: "rgba(255,77,79,0.3)", shadowBlur: 10 },
      dark: { opacity: 0.3 },
    },
    edgeStateStyles: {
      highlight: {
        stroke: "#ff4d4f",
        lineWidth: 4,
        opacity: 1,
        shadowColor: "rgba(255,77,79,0.2)",
        shadowBlur: 8,
        endArrow: { path: G6.Arrow.triangle(18, 22, 0), d: 22, fill: "#ff4d4f", stroke: "#ff4d4f", lineWidth: 1 },
      },
      dark: { opacity: 0.2 },
    },
    layout: getLayoutConfig(selectedLayout.value),
    animate: true,
    fitCenter: true,
  });

  // 交互：高亮邻居
  graph.on("node:click", (ev) => {
    const id = ev.item.getID();
    highlightNeighbors(id);
  });
  graph.on("canvas:click", () => clearHighlight());

  graph.data(toG6Data());
  graph.render();
  graph.fitView(30);

  // 容器自适应
  const ro = new ResizeObserver(() => {
    if (!graph || graph.get("destroyed")) return;
    const { clientWidth: w, clientHeight: h } = wrapRef.value;
    graph.changeSize(Math.max(300, w), Math.max(240, h));
  });
  ro.observe(wrapRef.value);
  graph.__ro__ = ro;
}

/* =============== 高亮 =============== */

function clearHighlight() {
  if (!graph) return;
  graph.setAutoPaint(false);
  graph.getNodes().forEach((n) => graph.clearItemStates(n));
  graph.getEdges().forEach((e) => graph.clearItemStates(e));
  graph.paint();
  graph.setAutoPaint(true);
}

function highlightNeighbors(centerId) {
  if (!graph) return;
  clearHighlight();

  const center = graph.findById(centerId);
  if (!center) return;

  const neighborNodes = graph.getNeighbors(center, "both");
  const relatedEdges = graph.getEdges().filter((e) => {
    const m = e.getModel();
    return m.source === centerId || m.target === centerId;
  });

  graph.getNodes().forEach((n) => graph.setItemState(n, "dark", true));
  graph.getEdges().forEach((e) => graph.setItemState(e, "dark", true));

  graph.setItemState(center, "dark", false);
  graph.setItemState(center, "highlight", true);
  neighborNodes.forEach((n) => graph.setItemState(n, "dark", false));
  relatedEdges.forEach((e) => {
    graph.setItemState(e, "dark", false);
    graph.setItemState(e, "highlight", true);
  });
}

/* =============== 生命周期 =============== */

onMounted(build);

onBeforeUnmount(() => {
  if (graph?.__ro__) graph.__ro__.disconnect?.();
  graph?.destroy?.();
});

// 数据变化：刷新
watch(
  () => [props.nodes, props.links],
  () => {
    if (!graph) return;
    graph.changeData(toG6Data());
    applyLayout();
  },
  { deep: true }
);
</script>

<style scoped>
.g6-panel {
  position: relative;
  width: 100%;
  height: 100%;
}

.toolbar {
  position: absolute;
  z-index: 10;
  top: 10px;
  left: 10px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  padding: 6px 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.06);
  display: flex;
  gap: 6px;
  align-items: center;
  font-size: 13px;
}
.toolbar select {
  appearance: none;
  border: 1px solid #d1d5db;
  background: #fff;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 13px;
  outline: none;
}

.g6-wrap {
  width: 100%;
  height: 100%;
  min-height: 480px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Tooltip 外观 */
.g6-tt {
  background: rgba(20, 24, 36, 0.92);
  color: #fff;
  padding: 8px 10px;
  border-radius: 8px;
  font-size: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.18);
  max-width: 240px;
}
.g6-tt .name {
  font-weight: 600;
  margin-bottom: 2px;
}
.g6-tt .sub {
  opacity: 0.9;
}
</style>

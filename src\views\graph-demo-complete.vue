<template>
  <div class="demo-container">
    <div class="demo-header">
      <h1>G6 v5 图形布局演示</h1>
      <p>展示三种不同的图形布局算法：径向布局、同心圆布局、层次布局</p>
    </div>

    <div class="demo-controls">
      <div class="control-group">
        <label>选择数据集：</label>
        <select v-model="selectedDataset" @change="switchDataset">
          <option value="hierarchy">层次结构数据</option>
          <option value="network">网络关系数据</option>
          <option value="tree">树形数据</option>
        </select>
      </div>
      
      <div class="control-group">
        <label>节点大小：</label>
        <input 
          type="range" 
          v-model="nodeSize" 
          min="10" 
          max="30" 
          @input="updateNodeSize"
        />
        <span>{{ nodeSize }}px</span>
      </div>
      
      <div class="control-group">
        <label>显示箭头：</label>
        <input type="checkbox" v-model="showArrows" />
      </div>
    </div>

    <div class="graph-container">
      <GraphViewG6 
        :nodes="currentNodes" 
        :links="currentLinks" 
        :showArrows="showArrows" 
        :arrowSize="20"
        :r="nodeSize"
        :key="graphKey"
      />
    </div>

    <div class="layout-info">
      <div class="info-card">
        <h3>径向布局 (Radial)</h3>
        <p>以度数最高的节点为中心，其他节点按层级向外辐射排列。适合展示中心化网络和层次结构。</p>
      </div>
      <div class="info-card">
        <h3>同心圆布局 (Concentric)</h3>
        <p>使用BFS算法计算节点层级，按距离中心节点的远近排列在同心圆上。适合展示影响力分布。</p>
      </div>
      <div class="info-card">
        <h3>层次布局 (Dagre)</h3>
        <p>自上而下的有向图布局，自动处理节点层级和间距。适合展示流程图和依赖关系。</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import GraphViewG6 from "@/components/graph/g6v5.vue";

const selectedDataset = ref("hierarchy");
const nodeSize = ref(16);
const showArrows = ref(true);
const graphKey = ref(0);

// 层次结构数据
const hierarchyData = {
  nodes: [
    { id: "ceo", nodeName: "CEO", label: "高管", entityId: "CEO001" },
    { id: "cto", nodeName: "CTO", label: "高管", entityId: "CTO001" },
    { id: "cfo", nodeName: "CFO", label: "高管", entityId: "CFO001" },
    { id: "dev1", nodeName: "开发主管", label: "主管", entityId: "DEV001" },
    { id: "dev2", nodeName: "测试主管", label: "主管", entityId: "TEST001" },
    { id: "fin1", nodeName: "财务主管", label: "主管", entityId: "FIN001" },
    { id: "emp1", nodeName: "前端工程师", label: "员工", entityId: "EMP001" },
    { id: "emp2", nodeName: "后端工程师", label: "员工", entityId: "EMP002" },
    { id: "emp3", nodeName: "测试工程师", label: "员工", entityId: "EMP003" },
    { id: "emp4", nodeName: "会计", label: "员工", entityId: "EMP004" },
  ],
  links: [
    { id: "l1", name: "管理", source: "ceo", target: "cto" },
    { id: "l2", name: "管理", source: "ceo", target: "cfo" },
    { id: "l3", name: "管理", source: "cto", target: "dev1" },
    { id: "l4", name: "管理", source: "cto", target: "dev2" },
    { id: "l5", name: "管理", source: "cfo", target: "fin1" },
    { id: "l6", name: "管理", source: "dev1", target: "emp1" },
    { id: "l7", name: "管理", source: "dev1", target: "emp2" },
    { id: "l8", name: "管理", source: "dev2", target: "emp3" },
    { id: "l9", name: "管理", source: "fin1", target: "emp4" },
  ]
};

// 网络关系数据
const networkData = {
  nodes: [
    { id: "hub", nodeName: "核心节点", label: "中心", entityId: "HUB001" },
    { id: "node1", nodeName: "节点A", label: "普通", entityId: "N001" },
    { id: "node2", nodeName: "节点B", label: "普通", entityId: "N002" },
    { id: "node3", nodeName: "节点C", label: "普通", entityId: "N003" },
    { id: "node4", nodeName: "节点D", label: "普通", entityId: "N004" },
    { id: "node5", nodeName: "节点E", label: "普通", entityId: "N005" },
    { id: "sub1", nodeName: "子节点1", label: "子节点", entityId: "S001" },
    { id: "sub2", nodeName: "子节点2", label: "子节点", entityId: "S002" },
    { id: "sub3", nodeName: "子节点3", label: "子节点", entityId: "S003" },
  ],
  links: [
    { id: "e1", name: "连接", source: "hub", target: "node1" },
    { id: "e2", name: "连接", source: "hub", target: "node2" },
    { id: "e3", name: "连接", source: "hub", target: "node3" },
    { id: "e4", name: "连接", source: "hub", target: "node4" },
    { id: "e5", name: "连接", source: "hub", target: "node5" },
    { id: "e6", name: "关联", source: "node1", target: "node2" },
    { id: "e7", name: "关联", source: "node2", target: "node3" },
    { id: "e8", name: "扩展", source: "node1", target: "sub1" },
    { id: "e9", name: "扩展", source: "node3", target: "sub2" },
    { id: "e10", name: "扩展", source: "node4", target: "sub3" },
  ]
};

// 树形数据
const treeData = {
  nodes: [
    { id: "root", nodeName: "根节点", label: "根", entityId: "ROOT001" },
    { id: "branch1", nodeName: "分支1", label: "分支", entityId: "B001" },
    { id: "branch2", nodeName: "分支2", label: "分支", entityId: "B002" },
    { id: "branch3", nodeName: "分支3", label: "分支", entityId: "B003" },
    { id: "leaf1", nodeName: "叶子1", label: "叶子", entityId: "L001" },
    { id: "leaf2", nodeName: "叶子2", label: "叶子", entityId: "L002" },
    { id: "leaf3", nodeName: "叶子3", label: "叶子", entityId: "L003" },
    { id: "leaf4", nodeName: "叶子4", label: "叶子", entityId: "L004" },
    { id: "leaf5", nodeName: "叶子5", label: "叶子", entityId: "L005" },
    { id: "leaf6", nodeName: "叶子6", label: "叶子", entityId: "L006" },
  ],
  links: [
    { id: "t1", name: "分支", source: "root", target: "branch1" },
    { id: "t2", name: "分支", source: "root", target: "branch2" },
    { id: "t3", name: "分支", source: "root", target: "branch3" },
    { id: "t4", name: "叶子", source: "branch1", target: "leaf1" },
    { id: "t5", name: "叶子", source: "branch1", target: "leaf2" },
    { id: "t6", name: "叶子", source: "branch2", target: "leaf3" },
    { id: "t7", name: "叶子", source: "branch2", target: "leaf4" },
    { id: "t8", name: "叶子", source: "branch3", target: "leaf5" },
    { id: "t9", name: "叶子", source: "branch3", target: "leaf6" },
  ]
};

const datasets = {
  hierarchy: hierarchyData,
  network: networkData,
  tree: treeData,
};

const currentNodes = computed(() => {
  return datasets[selectedDataset.value].nodes.map((n) => ({
    ...n,
    attrs: [
      { code: "name", name: "名称", value: n.nodeName, sortId: 0, isSystem: false },
      { code: "type", name: "类型", value: n.label, sortId: 1, isSystem: false },
      { code: "id", name: "编号", value: n.entityId, sortId: 2, isSystem: false },
    ],
  }));
});

const currentLinks = computed(() => {
  return datasets[selectedDataset.value].links;
});

function switchDataset() {
  // 强制重新渲染图形组件
  graphKey.value++;
}

function updateNodeSize() {
  // 强制重新渲染图形组件
  graphKey.value++;
}
</script>

<style scoped>
.demo-container {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
}

.demo-header {
  text-align: center;
  margin-bottom: 20px;
}

.demo-header h1 {
  margin: 0 0 10px 0;
  color: #1e293b;
  font-size: 28px;
  font-weight: 700;
}

.demo-header p {
  margin: 0;
  color: #64748b;
  font-size: 16px;
}

.demo-controls {
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-group label {
  font-weight: 600;
  color: #374151;
  white-space: nowrap;
}

.control-group select,
.control-group input[type="range"] {
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.control-group input[type="checkbox"] {
  transform: scale(1.2);
}

.graph-container {
  flex: 1;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  margin-bottom: 20px;
}

.layout-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.info-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #3b82f6;
}

.info-card h3 {
  margin: 0 0 10px 0;
  color: #1e293b;
  font-size: 18px;
  font-weight: 600;
}

.info-card p {
  margin: 0;
  color: #64748b;
  font-size: 14px;
  line-height: 1.5;
}
</style>
